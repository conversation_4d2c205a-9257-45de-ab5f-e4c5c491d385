import React from "react";
import { Mo<PERSON>, Tabs, Form, Input, Button, message, ConfigProvider } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { LOGIN } from "../../redux/slices/AuthSlice";
import { REGISTER } from "../../redux/slices/AccountSlice";
import {
  selectAuthLoading,
  selectAuthError,
} from "../../redux/slices/AuthSlice";

const { TabPane } = Tabs;

const AuthModal = ({ open, onClose }) => {
  const dispatch = useDispatch();
  const loading = useSelector(selectAuthLoading);
  const error = useSelector(selectAuthError);

  const [formLogin] = Form.useForm();
  const [formRegister] = Form.useForm();

  const handleLogin = (values) => {
    dispatch(LOGIN(values))
      .unwrap()
      .then(() => {
        onClose();
        message.success("Đăng nhập thành công!");
      })
      .catch(() => {error});
  };

  const handleRegister = (values) => {
    dispatch(REGISTER(values))
      .unwrap()
      .then(() => {
        onClose();
        message.success("Đăng ký thành công!");
      })
      .catch(() => {});
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: "#111",
          colorError: "#ef4444",
        },
      }}
    >
      <Modal
        open={open}
        onCancel={onClose}
        footer={null}
        centered
        className="rounded-2xl"
      >
        <Tabs
          defaultActiveKey="login"
          centered
          className="custom-tabs"
          items={[
            {
              label: (
                <span className="font-semibold italic text-lg hover:text-gray-800">
                  Đăng nhập
                </span>
              ),
              key: "login",
              children: (
                <Form
                  form={formLogin}
                  layout="vertical"
                  onFinish={handleLogin}
                  className="space-y-4"
                  requiredMark={false}
                >
                  <Form.Item
                    name="username"
                    label={<span className="font-medium italic">Tài khoản</span>}
                    rules={[{ required: true, message: "Nhập tài khoản!" }]}
                  >
                    <Input placeholder="Nhập tài khoản" />
                  </Form.Item>

                  <Form.Item
                    name="password"
                    label={<span className="font-medium italic">Mật khẩu</span>}
                    rules={[{ required: true, message: "Nhập mật khẩu!" }]}
                  >
                    <Input.Password placeholder="Nhập mật khẩu" />
                  </Form.Item>

                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    className="w-full rounded-xl font-semibold italic"
                  >
                    Đăng nhập
                  </Button>
                </Form>
              ),
            },
            {
              label: (
                <span className="font-semibold italic text-lg hover:text-gray-800">
                  Đăng ký
                </span>
              ),
              key: "register",
              children: (
                <Form
                  form={formRegister}
                  layout="vertical"
                  onFinish={handleRegister}
                  className="space-y-4"
                  requiredMark={false}
                >
                  <Form.Item
                    name="username"
                    label={<span className="font-medium italic">Tài khoản</span>}
                    rules={[{ required: true, message: "Nhập tài khoản!" }]}
                  >
                    <Input placeholder="Nhập tài khoản" />
                  </Form.Item>

                  <Form.Item
                    name="email"
                    label={<span className="font-medium italic">Email</span>}
                    rules={[
                      { required: true, message: "Nhập email!" },
                      { type: "email", message: "Email không hợp lệ!" },
                    ]}
                  >
                    <Input placeholder="Nhập email" />
                  </Form.Item>

                  <Form.Item
                    name="phone"
                    label={<span className="font-medium italic">Số điện thoại</span>}
                    rules={[{ required: true, message: "Nhập số điện thoại!" }]}
                  >
                    <Input placeholder="Nhập số điện thoại" />
                  </Form.Item>

                  <Form.Item
                    name="password"
                    label={<span className="font-medium italic">Mật khẩu</span>}
                    rules={[{ required: true, message: "Nhập mật khẩu!" }]}
                  >
                    <Input.Password placeholder="Nhập mật khẩu" />
                  </Form.Item>

                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    className="w-full rounded-xl font-semibold italic"
                  >
                    Đăng kí
                  </Button>
                </Form>
              ),
            },
          ]}
        />
      </Modal>

      <style>
        {`
          .custom-tabs .ant-tabs-tab {
            font-weight: 600;
            font-style: italic;
            font-size: 1.1rem;
          }
          .custom-tabs .ant-tabs-tab:hover {
            color: #111 !important;
          }
          .custom-tabs .ant-tabs-tab-active .ant-tabs-tab-btn {
            color: #111 !important;
          }
          .custom-tabs .ant-tabs-ink-bar {
            background: #111 !important;
          }
        `}
      </style>
    </ConfigProvider>
  );
};

export default AuthModal;
