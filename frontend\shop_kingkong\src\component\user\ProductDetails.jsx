import React from "react";
import { useParams } from "react-router-dom";

const ProductDetails = () => {
  const { id } = useParams();

  return (
    <div className="min-h-screen mx-auto pt-24 pb-12">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="image-section">
            <div className="main-image mb-4 bg-gray-400">
              <img
                src="https://via.placeholder.com/300x300"
                alt="Main"
                className="w-full h-96 object-cover "
              />
            </div>
            {/* Các ảnh nhỏ thumbnail */}
            <div className="thumbnail-images flex gap-2 overflow-x-auto">
              {thumbnails.map((thumb, index) => (
                <img
                  key={index}
                  src={thumb}
                  alt={`Thumbnail ${index}`}
                  className="w-20 h-20 object-cover rounded cursor-pointer border-2 hover:border-blue-500"
                  onClick={() => setMainImage(thumb)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetails;
